package com.yhl.scp.mps.proabnormalfeedback.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>ProAbnormalFeedbackDO</code>
 * <p>
 * 生产异常反馈DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-22 16:54:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProAbnormalFeedbackDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -52096254873763880L;

        
    /**
     * 主键ID
     */
    private String id;
        
    /**
     * 生产组织代码
     */
    private String organizationCode;
        
    /**
     * 生产组织名称
     */
    private String organizationName;
        
    /**
     * 异常反馈id
     */
    private String abnormalFeedbackId;
        
    /**
     * 提报时间
     */
    private Date submissionTime;
        
    /**
     * 预计关闭时间
     */
    private Date forecastCloseTime;
        
    /**
     * 实际关闭时间
     */
    private Date achieveCloseTime;
        
    /**
     * 生产工单号
     */
    private String productOrderCode;
        
    /**
     * 工序代码
     */
    private String operationCode;
        
    /**
     * 资源代码
     */
    private String resourceCode;
        
    /**
     * 资源名称
     */
    private String resourceName;
        
    /**
     * 故障类型
     */
    private String failureType;
        
    /**
     * 故障编码
     */
    private String failureCode;
        
    /**
     * 故障
     */
    private String failure;
        
    /**
     * 设备部位
     */
    private String equipmentPosition;
        
    /**
     * 问题描述
     */
    private String problemDescription;
        
    /**
     * 问题原因
     */
    private String problemReason;
        
    /**
     * 处理措施
     */
    private String treatmentMeasure;
        
    /**
     * 处理时长(分)
     */
    private Integer treatmentDuration;
    
    /**
     * 日历规则IDS
     */
    private String calendarRuleIds;

}
