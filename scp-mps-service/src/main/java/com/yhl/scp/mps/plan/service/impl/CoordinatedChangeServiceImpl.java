package com.yhl.scp.mps.plan.service.impl;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mps.plan.req.CoordinatedChangeReq;
import com.yhl.scp.mps.plan.service.CoordinatedChangeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <code>CoordinatedChangeServiceImpl</code>
 * <p>
 * CoordinatedChangeServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 23:36:41
 */
@Service
public class CoordinatedChangeServiceImpl implements CoordinatedChangeService {

    @Resource
    private DfpFeign dfpFeign;

    /**
     * 修改时需增加修改的限制，编辑时调用发货计划推移计算，
     * 当出现期末库存与最小安全库存差异小于0时则不允许变更，报错提示后进行数据回退；
     */
    @Override
    public void doConfirm(List<CoordinatedChangeReq> dataList) {
        String scenario = SystemHolder.getScenario();
        // 对象数据转换
        List<DeliveryPlanPublishedDTO> publishedList = new ArrayList<>();
        for (CoordinatedChangeReq item : dataList) {
            DeliveryPlanPublishedDTO publishedDTO = new DeliveryPlanPublishedDTO();
            publishedDTO.setId(item.getId());
            publishedDTO.setOemCode(item.getOemCode());
            publishedDTO.setDemandCategory(item.getDemandCategory());
            publishedDTO.setProductCode(item.getProductCode());
            publishedDTO.setCoordinationQuantity(item.getCoordinationQuantity());
            publishedDTO.setDemandQuantity(item.getDemandQuantity());
            publishedList.add(publishedDTO);
        }
        // 发货计划推移推移校验逻辑
        BaseResponse<Void> response = dfpFeign.inventoryMovementCheck(scenario, publishedList);
        if (Boolean.FALSE.equals(response.getSuccess())) {
            throw new BusinessException(response.getMsg());
        }
        dfpFeign.updateCoordinationQuantity(scenario, false, publishedList);
    }

}