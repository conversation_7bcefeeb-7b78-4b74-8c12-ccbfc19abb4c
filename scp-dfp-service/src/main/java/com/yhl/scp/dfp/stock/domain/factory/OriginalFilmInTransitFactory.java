package com.yhl.scp.dfp.stock.domain.factory;

import com.yhl.scp.dfp.stock.domain.entity.OriginalFilmInTransitDO;
import com.yhl.scp.dfp.stock.dto.OriginalFilmInTransitDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.OriginalFilmInTransitDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>OriginalFilmInTransitFactory</code>
 * <p>
 * 原片在途数据领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:38:25
 */
@Component
public class OriginalFilmInTransitFactory {

    @Resource
    private OriginalFilmInTransitDao originalFilmInTransitDao;

    OriginalFilmInTransitDO create(OriginalFilmInTransitDTO dto) {
        // TODO
        return null;
    }

}
