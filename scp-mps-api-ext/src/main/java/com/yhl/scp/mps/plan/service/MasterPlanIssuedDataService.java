package com.yhl.scp.mps.plan.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.plan.dto.MasterPlanIssuedDataDTO;
import com.yhl.scp.mps.plan.vo.MasterPlanIssuedDataVO;

import java.util.List;

/**
 * <code>MasterPlanIssuedDataService</code>
 * <p>
 * 主计划发布数据应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 09:39:39
 */
public interface MasterPlanIssuedDataService extends BaseService<MasterPlanIssuedDataDTO, MasterPlanIssuedDataVO> {

    /**
     * 查询所有
     *
     * @return list {@link MasterPlanIssuedDataVO}
     */
    List<MasterPlanIssuedDataVO> selectAll();
    
    /**
     * 维护主计划版本下的数据存储
     * @param userId
     * @param userName 
     */
    String txNewCreateForMasterPlan(String userId, String userName);

	void doRollBackData(String publishedLogId);

}
