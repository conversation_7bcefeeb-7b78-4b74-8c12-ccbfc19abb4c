package com.yhl.scp.dfp.oem.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <code>OemInventoryVO</code>
 * <p>
 * 主机厂库存信息表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-14 16:03:08
 */
@ApiModel(value = "主机厂库存信息表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OemInventoryVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -71072155087509112L;

    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂车型编码
     */
    @ApiModelProperty(value = "主机厂车型编码")
    @FieldInterpretation(value = "主机厂车型编码")
    private String oemVehicleModelCode;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月", hidden = true)
    @FieldInterpretation(value = "年月")
    private Date inventoryTime;
    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量", hidden = true)
    @FieldInterpretation(value = "库存数量")
    private Integer inventoryQty;
    /**
     * 动态列
     */
    @ApiModelProperty(value = "动态列：年月/数量")
    private List<OemInventoryDetail> detailList;

    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;

    @Override
    public void clean() {

    }
}
