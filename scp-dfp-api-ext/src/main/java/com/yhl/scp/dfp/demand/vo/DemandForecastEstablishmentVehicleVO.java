package com.yhl.scp.dfp.demand.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <code>DemandForecastEstablishmentVehicleVO</code>
 * <p>
 * 业务预测试制车型返回数据格式
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 14:42:34
 */
@ApiModel(value = "业务预测试制车型返回数据格式")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DemandForecastEstablishmentVehicleVO implements Serializable {

    private String productCode;

    private String productName;

    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "取数位置")
    private String accessPosition;

    @ApiModelProperty(value = "综合系数")
    private BigDecimal comprehensiveCoefficient;

    @ApiModelProperty(value = "动态列详情")
    private List<EstablishmentDynamicDataDetailVO> detailVOList;

    @ApiModelProperty(value = "物品详情")
    private List<DemandForecastEstablishmentProductVO> productVOList;
}
