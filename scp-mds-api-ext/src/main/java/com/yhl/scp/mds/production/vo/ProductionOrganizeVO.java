package com.yhl.scp.mds.production.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "生产组织管理VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductionOrganizeVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -85890747623190045L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @FieldInterpretation(value = "主键ID")
    private String id;
    /**
     * 生产组织编码
     */
    @ApiModelProperty(value = "生产组织编码")
    @FieldInterpretation(value = "生产组织编码")
    private String productionOrgCode;
    /**
     * 生产组织名称
     */
    @ApiModelProperty(value = "生产组织名称")
    @FieldInterpretation(value = "生产组织名称")
    private String productionOrgName;
    /**
     * 计划区域
     */
    @ApiModelProperty(value = "计划区域")
    @FieldInterpretation(value = "计划区域")
    private String planArea;
    /**
     * 上次更新时间
     */
    @ApiModelProperty(value = "上次更新时间")
    @FieldInterpretation(value = "上次更新时间")
    private Date lastUpdateTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    @FieldInterpretation(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @FieldInterpretation(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @FieldInterpretation(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @FieldInterpretation(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @FieldInterpretation(value = "修改时间")
    private Date modifyTime;
    /**
     * 数据表版本号
     */
    @ApiModelProperty(value = "数据表版本号")
    @FieldInterpretation(value = "数据表版本号")
    private Integer versionValue;

    @Override
    public void clean() {

    }
}
