package com.yhl.scp.ips.system;

import java.io.Serializable;

/**
 * <code>DefaultTenant</code>
 * <p>
 * 默认租户
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 20:11:08
 */
public class DefaultTenant implements Serializable {

    private static final long serialVersionUID = 596812229518504193L;

    private String tenantId;

    private String moduleCode;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }
}
