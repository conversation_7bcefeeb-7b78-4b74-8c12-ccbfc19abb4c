package com.yhl.scp.mrp.report.glassInventoryDynamic.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.report.glassInventoryDynamic.convertor.GlassInventoryDynamicBasicReportConvertor;
import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.entity.GlassInventoryDynamicBasicReportDO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.service.GlassInventoryDynamicBasicReportDomainService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.dto.GlassInventoryDynamicBasicReportDTO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.dao.GlassInventoryDynamicBasicReportDao;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicBasicReportPO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.service.GlassInventoryDynamicBasicReportService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicBasicReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>GlassInventoryDynamicBasicReportServiceImpl</code>
 * <p>
 * 原片库存动态基础信息表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 10:57:25
 */
@Slf4j
@Service
public class GlassInventoryDynamicBasicReportServiceImpl extends AbstractService implements GlassInventoryDynamicBasicReportService {

    @Resource
    private GlassInventoryDynamicBasicReportDao glassInventoryDynamicBasicReportDao;

    @Resource
    private GlassInventoryDynamicBasicReportDomainService glassInventoryDynamicBasicReportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(GlassInventoryDynamicBasicReportDTO glassInventoryDynamicBasicReportDTO) {
        // 0.数据转换
        GlassInventoryDynamicBasicReportDO glassInventoryDynamicBasicReportDO = GlassInventoryDynamicBasicReportConvertor.INSTANCE.dto2Do(glassInventoryDynamicBasicReportDTO);
        GlassInventoryDynamicBasicReportPO glassInventoryDynamicBasicReportPO = GlassInventoryDynamicBasicReportConvertor.INSTANCE.dto2Po(glassInventoryDynamicBasicReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryDynamicBasicReportDomainService.validation(glassInventoryDynamicBasicReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassInventoryDynamicBasicReportPO);
        glassInventoryDynamicBasicReportDao.insert(glassInventoryDynamicBasicReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(GlassInventoryDynamicBasicReportDTO glassInventoryDynamicBasicReportDTO) {
        // 0.数据转换
        GlassInventoryDynamicBasicReportDO glassInventoryDynamicBasicReportDO = GlassInventoryDynamicBasicReportConvertor.INSTANCE.dto2Do(glassInventoryDynamicBasicReportDTO);
        GlassInventoryDynamicBasicReportPO glassInventoryDynamicBasicReportPO = GlassInventoryDynamicBasicReportConvertor.INSTANCE.dto2Po(glassInventoryDynamicBasicReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryDynamicBasicReportDomainService.validation(glassInventoryDynamicBasicReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassInventoryDynamicBasicReportPO);
        glassInventoryDynamicBasicReportDao.update(glassInventoryDynamicBasicReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassInventoryDynamicBasicReportDTO> list) {
        List<GlassInventoryDynamicBasicReportPO> newList = GlassInventoryDynamicBasicReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassInventoryDynamicBasicReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassInventoryDynamicBasicReportDTO> list) {
        List<GlassInventoryDynamicBasicReportPO> newList = GlassInventoryDynamicBasicReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassInventoryDynamicBasicReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassInventoryDynamicBasicReportDao.deleteBatch(idList);
        }
        return glassInventoryDynamicBasicReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassInventoryDynamicBasicReportVO selectByPrimaryKey(String id) {
        GlassInventoryDynamicBasicReportPO po = glassInventoryDynamicBasicReportDao.selectByPrimaryKey(id);
        return GlassInventoryDynamicBasicReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "GLASS_INVENTORY_DYNAMIC_BASIC_REPORT")
    public List<GlassInventoryDynamicBasicReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "GLASS_INVENTORY_DYNAMIC_BASIC_REPORT")
    public List<GlassInventoryDynamicBasicReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassInventoryDynamicBasicReportVO> dataList = glassInventoryDynamicBasicReportDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassInventoryDynamicBasicReportServiceImpl target = SpringBeanUtils.getBean(GlassInventoryDynamicBasicReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlassInventoryDynamicBasicReportVO> selectByParams(Map<String, Object> params) {
        List<GlassInventoryDynamicBasicReportPO> list = glassInventoryDynamicBasicReportDao.selectByParams(params);
        return GlassInventoryDynamicBasicReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassInventoryDynamicBasicReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<GlassInventoryDynamicBasicReportVO> invocation(List<GlassInventoryDynamicBasicReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
