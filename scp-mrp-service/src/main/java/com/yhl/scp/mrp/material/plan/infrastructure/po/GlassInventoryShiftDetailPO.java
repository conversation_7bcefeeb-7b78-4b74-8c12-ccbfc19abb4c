package com.yhl.scp.mrp.material.plan.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>GlassInventoryShiftDetailPO</code>
 * <p>
 * 物料库存推移PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:08:47
 */
public class GlassInventoryShiftDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -83220584251289227L;

    /**
     * 物料计划版本id
     */
    private String inventoryShiftDataId;
    /**
     * 库存点编码
     */
    private String stockPointCode;
    /**
     * 库存点类型
     */
    private String stockPointType;
    /**
     * 库存推移日期
     */
    private Date inventoryDate;
    /**
     * 所有浮法厂库存
     */
    private BigDecimal allFfInventory;
    /**
     * 最小安全库存水位
     */
    private BigDecimal safetyStockLevelMin;
    /**
     * 标准安全库存水位
     */
    private BigDecimal safetyStockLevelStandard;
    /**
     * 最大安全库存水位
     */
    private BigDecimal safetyStockLevelMax;
    /**
     * 期初库存
     */
    private BigDecimal openingInventory;
    /**
     * 本厂期初库存
     */
    private BigDecimal bcOpeningInventory;
    /**
     * 毛需求
     */
    private BigDecimal demandQuantity;
    /**
     * 计划调拨-浮法送柜
     */
    private BigDecimal adjustQuantityFromFloat;
    /**
     * 已发布调拨计划到货
     */
    private BigDecimal inputQuantity;
    /**
     * 计划调拨-码头送柜
     */
    private BigDecimal adjustQuantityFromPort;
    /**
     * 在途码头送柜
     */
    private BigDecimal transitQuantityFromPort;
    /**
     * 在途浮法送柜
     */
    private BigDecimal transitQuantityFromFloat;
    /**
     * 已发布调拨运入至本厂
     * 已发布码头送柜
     */
    private BigDecimal outputQuantityToBc;
    /**
     * 已发布调拨运出至码头
     */
    private BigDecimal outputQuantityToPort;
    /**
     * 计划调拨运出至本厂
     */
    private BigDecimal decisionOutputQuantityToBc;
    /**
     * 计划调拨运出至码头
     */
    private BigDecimal decisionOutputQuantityToPort;
    /**
     * 供应替代
     */
    private BigDecimal useReplaceQuantity;
    /**
     * 替代其他需求
     */
    private BigDecimal usedAsReplaceQuantity;
    /**
     * 补库缺口
     */
    private BigDecimal safetyStockGap;
    /**
     * 期末库存
     */
    private BigDecimal endingInventory;

    public String getInventoryShiftDataId() {
        return inventoryShiftDataId;
    }

    public void setInventoryShiftDataId(String inventoryShiftDataId) {
        this.inventoryShiftDataId = inventoryShiftDataId;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getStockPointType() {
        return stockPointType;
    }

    public void setStockPointType(String stockPointType) {
        this.stockPointType = stockPointType;
    }

    public Date getInventoryDate() {
        return inventoryDate;
    }

    public void setInventoryDate(Date inventoryDate) {
        this.inventoryDate = inventoryDate;
    }

    public BigDecimal getAllFfInventory() {
        return allFfInventory;
    }

    public void setAllFfInventory(BigDecimal allFfInventory) {
        this.allFfInventory = allFfInventory;
    }

    public BigDecimal getSafetyStockLevelMin() {
        return safetyStockLevelMin;
    }

    public void setSafetyStockLevelMin(BigDecimal safetyStockLevelMin) {
        this.safetyStockLevelMin = safetyStockLevelMin;
    }

    public BigDecimal getSafetyStockLevelStandard() {
        return safetyStockLevelStandard;
    }

    public void setSafetyStockLevelStandard(BigDecimal safetyStockLevelStandard) {
        this.safetyStockLevelStandard = safetyStockLevelStandard;
    }

    public BigDecimal getSafetyStockLevelMax() {
        return safetyStockLevelMax;
    }

    public void setSafetyStockLevelMax(BigDecimal safetyStockLevelMax) {
        this.safetyStockLevelMax = safetyStockLevelMax;
    }

    public BigDecimal getOpeningInventory() {
        return openingInventory;
    }

    public void setOpeningInventory(BigDecimal openingInventory) {
        this.openingInventory = openingInventory;
    }

    public BigDecimal getBcOpeningInventory() {
        return bcOpeningInventory;
    }

    public void setBcOpeningInventory(BigDecimal bcOpeningInventory) {
        this.bcOpeningInventory = bcOpeningInventory;
    }

    public BigDecimal getDemandQuantity() {
        return demandQuantity;
    }

    public void setDemandQuantity(BigDecimal demandQuantity) {
        this.demandQuantity = demandQuantity;
    }

    public BigDecimal getAdjustQuantityFromFloat() {
        return adjustQuantityFromFloat;
    }

    public void setAdjustQuantityFromFloat(BigDecimal adjustQuantityFromFloat) {
        this.adjustQuantityFromFloat = adjustQuantityFromFloat;
    }

    public BigDecimal getInputQuantity() {
        return inputQuantity;
    }

    public void setInputQuantity(BigDecimal inputQuantity) {
        this.inputQuantity = inputQuantity;
    }

    public BigDecimal getAdjustQuantityFromPort() {
        return adjustQuantityFromPort;
    }

    public void setAdjustQuantityFromPort(BigDecimal adjustQuantityFromPort) {
        this.adjustQuantityFromPort = adjustQuantityFromPort;
    }

    public BigDecimal getTransitQuantityFromPort() {
        return transitQuantityFromPort;
    }

    public void setTransitQuantityFromPort(BigDecimal transitQuantityFromPort) {
        this.transitQuantityFromPort = transitQuantityFromPort;
    }

    public BigDecimal getTransitQuantityFromFloat() {
        return transitQuantityFromFloat;
    }

    public void setTransitQuantityFromFloat(BigDecimal transitQuantityFromFloat) {
        this.transitQuantityFromFloat = transitQuantityFromFloat;
    }

    public BigDecimal getOutputQuantityToBc() {
        return outputQuantityToBc;
    }

    public void setOutputQuantityToBc(BigDecimal outputQuantityToBc) {
        this.outputQuantityToBc = outputQuantityToBc;
    }

    public BigDecimal getOutputQuantityToPort() {
        return outputQuantityToPort;
    }

    public void setOutputQuantityToPort(BigDecimal outputQuantityToPort) {
        this.outputQuantityToPort = outputQuantityToPort;
    }

    public BigDecimal getDecisionOutputQuantityToBc() {
        return decisionOutputQuantityToBc;
    }

    public void setDecisionOutputQuantityToBc(BigDecimal decisionOutputQuantityToBc) {
        this.decisionOutputQuantityToBc = decisionOutputQuantityToBc;
    }

    public BigDecimal getDecisionOutputQuantityToPort() {
        return decisionOutputQuantityToPort;
    }

    public void setDecisionOutputQuantityToPort(BigDecimal decisionOutputQuantityToPort) {
        this.decisionOutputQuantityToPort = decisionOutputQuantityToPort;
    }

    public BigDecimal getUseReplaceQuantity() {
        return useReplaceQuantity;
    }

    public void setUseReplaceQuantity(BigDecimal useReplaceQuantity) {
        this.useReplaceQuantity = useReplaceQuantity;
    }

    public BigDecimal getUsedAsReplaceQuantity() {
        return usedAsReplaceQuantity;
    }

    public void setUsedAsReplaceQuantity(BigDecimal usedAsReplaceQuantity) {
        this.usedAsReplaceQuantity = usedAsReplaceQuantity;
    }

    public BigDecimal getSafetyStockGap() {
        return safetyStockGap;
    }

    public void setSafetyStockGap(BigDecimal safetyStockGap) {
        this.safetyStockGap = safetyStockGap;
    }

    public BigDecimal getEndingInventory() {
        return endingInventory;
    }

    public void setEndingInventory(BigDecimal endingInventory) {
        this.endingInventory = endingInventory;
    }

}
