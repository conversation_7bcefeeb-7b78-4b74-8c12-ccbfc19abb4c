package com.yhl.scp.mrp.material.plan.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <code>MaterialPlanNeedReleaseMonthDTO</code>
 * <p>
 * 要货计划-下发要货计划发布（月）DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 14:44:04
 */
@ApiModel(value = "要货计划-下发要货计划发布（月）DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPlanNeedReleaseMonthDTO implements Serializable {
    @JsonProperty("Service")
    private Service service;

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Service implements Serializable {
        @JsonProperty("Route")
        private Route route;

        @JsonProperty("Data")
        private Data data;
    }

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Route implements Serializable {
        @JsonProperty("SerialNO")
        private String serialNO;

        @JsonProperty("ServiceID")
        private String serviceID;

        @JsonProperty("SourceSysID")
        private String sourceSysID;

        @JsonProperty("ServiceTime")
        private String serviceTime;
    }

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Data implements Serializable {
        @JsonProperty("Control")
        private Control control;

        @JsonProperty("Request")
        private Request request;
    }

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Control implements Serializable {
        @JsonProperty("PathVariable")
        private String pathVariable;
    }

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Request implements Serializable {
        @JsonProperty("rollPredictionList")
        private RollPredictionList rollPredictionList;
    }

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RollPredictionList implements Serializable {
        @JsonProperty("RollPrediction")
        private List<RollPrediction> rollPrediction;
    }

    @lombok.Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RollPrediction implements Serializable {
        @JsonProperty("PredictionNo")
        private String predictionNo;

        @JsonProperty("version")
        private String version;

        @JsonProperty("orgCode")
        private String orgCode;

        @JsonProperty("orgName")
        private String orgName;

        @JsonProperty("publisher")
        private String publisher;

        @JsonProperty("publishDate")
        private String publishDate;

        @JsonProperty("supplierCode")
        private String supplierCode;

        @JsonProperty("supplierName")
        private String supplierName;

        @JsonProperty("itemCode")
        private String itemCode;

        @JsonProperty("itemName")
        private String itemName;

        @JsonProperty("requireDate")
        private String requireDate;

        @JsonProperty("requireNum")
        private String requireNum;
    }
}
