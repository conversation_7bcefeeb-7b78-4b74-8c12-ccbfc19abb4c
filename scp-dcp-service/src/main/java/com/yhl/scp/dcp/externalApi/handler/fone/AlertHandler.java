package com.yhl.scp.dcp.externalApi.handler.fone;

import cn.hutool.core.map.MapUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.warning.dto.WarningSqlSettingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * description:
 * author：李杰 预警配置-发送邮件
 * email: <EMAIL>
 * date: 2025/3/1
 */
@Component
@Slf4j
public class AlertHandler extends SyncDataHandler<List> {

    @Resource
    private MessageHandler messageHandler;

    public String TYPE_EMAIL = "EMAIL";
    public String TYPE_SMS = "SMS";
    public String TYPE_WX = "WX";

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("发送预警信息:{},{}", apiConfigVO, params);
        }
        //获取传递前的数据
        Object mainObject = params.get("mainData");
        List<Map<String, Object>> mapList = null;
        WarningSqlSettingDTO warningSqlSettingDTO = JSONObject.parseObject(JSONObject.toJSONString(mainObject), WarningSqlSettingDTO.class);
        if ("1".equals(warningSqlSettingDTO.getSendWay())){
            Object object = params.get("data");
            if (object instanceof List) {
                mapList = (List<Map<String, Object>>) object;
            }
        }
        Date date = new Date();
        // 生成邮件消息
        String messageInfo = createMessageBody(warningSqlSettingDTO, date);
        Map<String, Object> messageParamMap = new HashMap<>();
        messageParamMap.put("messageInfo", messageInfo);
        // 生成邮件附件
        if ("1".equals(warningSqlSettingDTO.getSendWay())) {
            File file = createAttachment(warningSqlSettingDTO, mapList, date);
            //有数据前提才发送文件
            if ("1".equals(warningSqlSettingDTO.getWarnType())){
                messageParamMap.put("file", file);
            }
            try {
                return messageHandler.handle(messageParamMap);
            } catch (Exception e) {
                log.error("发送预警信息报错:{},原因：{}", e.getMessage(), e.getCause());
                throw new BusinessException("发送预警信息报错:{},原因：{}", e.getMessage(), e.getCause());
            } finally {
                if (file != null && file.exists()) {
                    boolean delete = file.delete();
                    if (!delete) {
                        log.error("删除附件失败：" + file.getAbsolutePath());
                    }
                }
            }
        } else {
            return messageHandler.handle(messageParamMap);
        }
    }

    @Override
    protected List convertData(String body) {
        return Collections.emptyList();
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List list) {
        return "同步成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.FONE.getCode(), ApiCategoryEnum.EMAIL_MESSAGE.getCode());
    }

    /**
     * 获取邮箱中的excel
     *
     * @param warningSqlSettingDTO
     * @param mapList
     * @param date
     * @return
     */
    private File createAttachment(WarningSqlSettingDTO warningSqlSettingDTO, List<Map<String, Object>> mapList, Date date) {
        File file = null;
        ExcelWriter excelWriter = null;
        try {
            //设置excel名称
            String nameOrTitle = warningSqlSettingDTO.getWarningDescription() + "-" + DateUtils.format(date, DateUtils.DATE_FORMAT_14);
            String outFileName = nameOrTitle + ".xlsx";
            //定义文件以及excel
            file = new File(outFileName);
            excelWriter = new ExcelWriter(true);
            //获取表头
            Set<String> columns = new LinkedHashSet<>();
            for (Map<String, Object> map : mapList) {
                columns.addAll(map.keySet());
            }
            List<Map<String, Object>> newMapList = new ArrayList<>();
            // 写入内容
            for (Map<String, Object> map : mapList) {
                Map<String, Object> stagingData = map;
                Set<String> keys = map.keySet();
                for (String key : columns) {
                    if (!keys.contains(key)) {
                        stagingData.put(key, "");
                    }
                }
                newMapList.add(stagingData);
            }
            excelWriter.write(newMapList, true);
            excelWriter.flush(file);
            return file;
        } catch (Exception e) {
            log.error("生成excel文件报错：{}", e.getMessage());
            throw new BusinessException("生成excel文件报错：" + e.getMessage());
        } finally {
            if (excelWriter != null) {
                excelWriter.close();
            }
        }
    }

    /**
     * 获取邮件
     *
     * @param warningSqlSettingDTO
     * @param date
     * @return
     */
    private String createMessageBody(WarningSqlSettingDTO warningSqlSettingDTO, Date date) {
        if (Objects.isNull(warningSqlSettingDTO)) {
            log.error("主体对象数据为空");
            return "";
        }
        //塞入邮箱
        HashMap<String, Object> map = MapUtils.newHashMap();
        if ("1".equals(warningSqlSettingDTO.getSendWay())) {
            map.put("title", "预警配置:" + warningSqlSettingDTO.getWarningDescription() + "-" + DateUtils.format(date, DateUtils.DATE_FORMAT_14));
            map.put("content", StringUtils.isEmpty(warningSqlSettingDTO.getTextContent())
                    ? "请及时处理。" : warningSqlSettingDTO.getTextContent().replaceAll("\n", "<br>").replaceAll(" ", "&nbsp;"));
            map.put("type", TYPE_EMAIL);
            map.put("useTemplate", false);
            map.put("receiver", warningSqlSettingDTO.getReceiver().replace(",", ";"));
            map.put("cc", warningSqlSettingDTO.getCarbonCopy().replace(",", ";"));
            map.put("bcc", warningSqlSettingDTO.getBlindCarbonCopy().replace(",", ";"));
            log.info("当前邮箱数据map={}", map);
        } else if ("2".equals(warningSqlSettingDTO.getSendWay())) {
            map.put("type", TYPE_WX);
            map.put("title", "预警配置:" + warningSqlSettingDTO.getWarningDescription() + "-" + DateUtils.format(date, DateUtils.DATE_FORMAT_14));
            map.put("content", StringUtils.isEmpty(warningSqlSettingDTO.getTextContent())
                    ? "请及时处理。" : warningSqlSettingDTO.getTextContent().replaceAll("\n", "<br>").replaceAll(" ", "&nbsp;"));
            map.put("receiver", warningSqlSettingDTO.getStaffCode().replace(",", ";"));
            map.put("agentId", "1000206");
            log.info("当前企微数据map={}", map);
        }
        return JSON.toJSONString(map);
    }
}
